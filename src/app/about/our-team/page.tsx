'use client'

import { motion } from 'framer-motion'
import { Card, CardContent } from '@/components/ui/Card'
import { Button } from '@/components/ui/Button'
import Link from 'next/link'
import Image from 'next/image'
import { 
  Users, 
  GraduationCap, 
  Settings, 
  FileText, 
  Calculator, 
  Monitor, 
  Calendar, 
  ShoppingCart,
  Star,
  Award
} from 'lucide-react'

const fadeInUp = {
  initial: { opacity: 0, y: 60 },
  animate: { opacity: 1, y: 0 },
  transition: { duration: 0.6 }
}

const staggerContainer = {
  initial: {},
  animate: {
    transition: {
      staggerChildren: 0.1
    }
  }
}

export default function OurTeamPage() {
  const advisor = {
    name: '施朱娟',
    title: '指導老師',
    description: '提供專業指導與學術支持，引領團隊朝向永續發展目標前進',
    image: '/hssl_profile.jpg',
    responsibilities: ['學術指導', '專業諮詢', '計畫督導']
  }

  const studentGroups = [
    {
      name: '設備組',
      icon: Settings,
      description: '負責實驗設備維護與管理',
      responsibilities: ['設備維護', '安全管理', '器材採購'],
      color: 'from-blue-500 to-blue-600',
      bgColor: 'bg-blue-50',
      textColor: 'text-blue-700'
    },
    {
      name: '教學組',
      icon: GraduationCap,
      description: '規劃教學內容與課程設計',
      responsibilities: ['課程設計', '教材製作', '教學執行'],
      color: 'from-green-500 to-green-600',
      bgColor: 'bg-green-50',
      textColor: 'text-green-700'
    },
    {
      name: '文書美宣組',
      icon: FileText,
      description: '負責文件製作與宣傳設計',
      responsibilities: ['文件撰寫', '美術設計', '宣傳企劃'],
      color: 'from-purple-500 to-purple-600',
      bgColor: 'bg-purple-50',
      textColor: 'text-purple-700'
    },
    {
      name: '總務組',
      icon: Calculator,
      description: '管理財務與行政事務',
      responsibilities: ['財務管理', '行政事務', '物資管理'],
      color: 'from-orange-500 to-orange-600',
      bgColor: 'bg-orange-50',
      textColor: 'text-orange-700'
    },
    {
      name: '資訊組',
      icon: Monitor,
      description: '維護網站與資訊系統',
      responsibilities: ['網站維護', '系統管理', '技術支援'],
      color: 'from-indigo-500 to-indigo-600',
      bgColor: 'bg-indigo-50',
      textColor: 'text-indigo-700'
    },
    {
      name: '活動公關',
      icon: Calendar,
      description: '策劃活動與對外聯繫',
      responsibilities: ['活動策劃', '公關聯繫', '媒體宣傳'],
      color: 'from-pink-500 to-pink-600',
      bgColor: 'bg-pink-50',
      textColor: 'text-pink-700'
    },
    {
      name: '義賣規劃',
      icon: ShoppingCart,
      description: '規劃義賣活動與產品銷售',
      responsibilities: ['義賣策劃', '產品管理', '銷售執行'],
      color: 'from-teal-500 to-teal-600',
      bgColor: 'bg-teal-50',
      textColor: 'text-teal-700'
    }
  ]

  const teamValues = [
    {
      icon: Star,
      title: '團隊合作',
      description: '各組別密切配合，共同完成目標'
    },
    {
      icon: Award,
      title: '專業分工',
      description: '每個組別都有專業的職責分工'
    },
    {
      icon: Users,
      title: '學習成長',
      description: '在團隊中學習並共同成長'
    },
    {
      icon: GraduationCap,
      title: '知識傳承',
      description: '經驗與知識的傳承與分享'
    }
  ]

  return (
    <motion.div
      initial="initial"
      animate="animate"
      className="min-h-screen bg-gradient-to-br from-cream via-green-50 to-cream"
    >
      {/* Hero Section */}
      <section className="relative py-20 px-4 sm:px-6 lg:px-8 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-br from-green-600 via-green-700 to-green-800"></div>
        <div className="absolute inset-0 bg-black/20"></div>
        
        <div className="relative max-w-7xl mx-auto text-center">
          <motion.div {...fadeInUp}>
            <h1 className="text-4xl md:text-6xl font-bold text-white mb-6">
              我們的團隊
            </h1>
            <p className="text-xl md:text-2xl text-green-100 max-w-3xl mx-auto leading-relaxed">
              由指導老師帶領，七個專業組別分工合作，
              共同推動 HSSL 的環保教育使命
            </p>
          </motion.div>
        </div>
      </section>

      {/* Advisor Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            {...fadeInUp}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              指導老師
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              提供專業指導與學術支持，引領團隊朝向永續發展目標前進
            </p>
          </motion.div>

          <motion.div
            className="flex justify-center"
            {...fadeInUp}
          >
            <Card className="max-w-md border-0 shadow-xl bg-gradient-to-br from-cream to-green-50">
              <CardContent className="p-8 text-center">
                <div className="w-32 h-32 mx-auto mb-6 relative">
                  <Image
                    src={advisor.image}
                    alt={advisor.name}
                    fill
                    className="rounded-full object-cover border-4 border-green-200"
                  />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">{advisor.name}</h3>
                <p className="text-green-600 font-semibold mb-4">{advisor.title}</p>
                <p className="text-gray-600 mb-6">{advisor.description}</p>
                <div className="space-y-2">
                  {advisor.responsibilities.map((responsibility, idx) => (
                    <div key={idx} className="flex items-center justify-center text-sm text-gray-700">
                      <div className="w-2 h-2 rounded-full bg-green-500 mr-2"></div>
                      {responsibility}
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Student Groups Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8 bg-white/50">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            {...fadeInUp}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              學生組織
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              七個組別分工合作，各司其職，共同推動 HSSL 的環保教育使命
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6 lg:gap-8"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {studentGroups.map((group, index) => (
              <motion.div
                key={group.name}
                variants={fadeInUp}
              >
                <Card hover className="h-full border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-0">
                    <div className={`p-6 bg-gradient-to-br ${group.color} text-white`}>
                      <group.icon size={48} className="mb-4" />
                      <h3 className="text-xl font-bold mb-2">{group.name}</h3>
                      <p className="text-white/90 text-sm">{group.description}</p>
                    </div>
                    <div className="p-6">
                      <h4 className="font-semibold text-gray-900 mb-3">主要職責</h4>
                      <ul className="space-y-2">
                        {group.responsibilities.map((responsibility, idx) => (
                          <li key={idx} className="flex items-center text-sm text-gray-700">
                            <div className={`w-2 h-2 rounded-full ${group.bgColor} mr-3`}></div>
                            {responsibility}
                          </li>
                        ))}
                      </ul>
                    </div>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* Team Values Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-7xl mx-auto">
          <motion.div
            className="text-center mb-16"
            {...fadeInUp}
          >
            <h2 className="text-3xl md:text-4xl font-bold text-gray-900 mb-4">
              團隊價值
            </h2>
            <p className="text-lg text-gray-600 max-w-3xl mx-auto">
              我們的團隊文化與核心價值
            </p>
          </motion.div>

          <motion.div
            className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6"
            variants={staggerContainer}
            initial="initial"
            whileInView="animate"
            viewport={{ once: true }}
          >
            {teamValues.map((value, index) => (
              <motion.div
                key={value.title}
                variants={fadeInUp}
              >
                <Card hover className="h-full text-center border-0 shadow-lg hover:shadow-xl transition-all duration-300">
                  <CardContent className="p-6">
                    <div className="w-16 h-16 bg-green-100 rounded-full flex items-center justify-center mx-auto mb-4">
                      <value.icon size={32} className="text-green-600" />
                    </div>
                    <h3 className="text-lg font-bold text-gray-900 mb-3">{value.title}</h3>
                    <p className="text-gray-600 text-sm">{value.description}</p>
                  </CardContent>
                </Card>
              </motion.div>
            ))}
          </motion.div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 sm:px-6 lg:px-8">
        <div className="max-w-4xl mx-auto text-center">
          <motion.div {...fadeInUp}>
            <Card className="border-0 shadow-xl bg-gradient-to-br from-green-600 to-green-700">
              <CardContent className="p-12">
                <h3 className="text-3xl md:text-4xl font-bold text-white mb-6">
                  想要加入我們的團隊嗎？
                </h3>
                <p className="text-xl text-green-100 mb-8 leading-relaxed">
                  我們歡迎有熱忱的學生加入，一起為環保教育貢獻心力
                </p>
                <div className="flex flex-col sm:flex-row gap-4 justify-center">
                  <Button variant="secondary" size="lg" asChild>
                    <Link href="/about/contact">
                      聯絡我們
                    </Link>
                  </Button>
                  <Button variant="outline" size="lg" className="border-white text-white hover:bg-white/10" asChild>
                    <Link href="/about/what-we-do">
                      了解更多
                    </Link>
                  </Button>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>
    </motion.div>
  )
}
